{"cells": [{"cell_type": "markdown", "id": "6f5b43b3", "metadata": {}, "source": ["Deserialization is the process of converting data from one format to another.In other words, it's the act of taking data (that can be provided in a number of ways) to create and populate a new model instance. In the context of Pydantic, deserialization refers to the process of converting data from a dictionary or JSON object to a Pydantic model.Deserialization is the opposite of serialization. Serialization is the process of converting a Pydantic model to a dictionary or JSON object. Deserialization is done using the `model_validate` method.\n", "\n", "We already saw one way of doing this:"]}, {"cell_type": "code", "execution_count": 1, "id": "e6efefbf", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, ValidationError"]}, {"cell_type": "code", "execution_count": 2, "id": "e0c138e8", "metadata": {}, "outputs": [], "source": ["class Person(BaseModel):\n", "    first_name: str\n", "    last_name: str\n", "    age: int\n", "    city: str"]}, {"cell_type": "code", "execution_count": 3, "id": "9d8646f0", "metadata": {}, "outputs": [], "source": ["p = Person(first_name=\"<PERSON>\", last_name=\"<PERSON><PERSON>\", age=30, city=\"New York\")"]}, {"cell_type": "markdown", "id": "c906187f", "metadata": {}, "source": ["It may not look like deserialization, but in essence that's what happened - we went from data provided as named argument, to an initialized instance.\n", "\n", "We also saw that Pydantic will perform some validation as it does so.\n", "\n", "Pydantic can handle deserializing data in two other formats:\n", "\n", "- a Python dict\n", "- a JSON string\n", "Let's look at dict first:"]}, {"cell_type": "code", "execution_count": 6, "id": "ee23545f", "metadata": {}, "outputs": [], "source": ["data = {\n", "    \"first_name\": \"<PERSON>\",\n", "    \"last_name\": \"<PERSON>\",\n", "    \"age\": 84,\n", "    \"city\": \"London\"\n", "}"]}, {"cell_type": "markdown", "id": "dc3d6e67", "metadata": {}, "source": ["To deserialize a dictionary we could technically use unpacking this way:"]}, {"cell_type": "code", "execution_count": 7, "id": "31a4e618", "metadata": {}, "outputs": [{"data": {"text/plain": ["Person(first_name='<PERSON>', last_name='<PERSON>', age=84, city='London')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["Person(**data)"]}, {"cell_type": "markdown", "id": "f1b5b2b6", "metadata": {}, "source": ["But this is not recommended. It works for simple models, but once you start getting into more complex models that use composition, you might run into issues.\n", "\n", "Instead, Pydantic provides a specialized method to load data from a dict - the model_validate() method.\n", "\n", "And, in fact, the method name clearly indicates that Pydantic is not just loading up data, but is also performing validation."]}, {"cell_type": "code", "execution_count": 8, "id": "0b5f0331", "metadata": {}, "outputs": [{"data": {"text/plain": ["Person(first_name='<PERSON>', last_name='<PERSON>', age=84, city='London')"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["p = Person.model_validate(data)\n", "p"]}, {"cell_type": "markdown", "id": "decab5eb", "metadata": {}, "source": ["The same validation exceptions we saw earlier will be raised when we have validation issues:"]}, {"cell_type": "code", "execution_count": 9, "id": "278fc891", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3 validation errors for Person\n", "first_name\n", "  Field required [type=missing, input_value={'last_name': '<PERSON>'}, input_type=dict]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/missing\n", "age\n", "  Field required [type=missing, input_value={'last_name': '<PERSON>'}, input_type=dict]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/missing\n", "city\n", "  Field required [type=missing, input_value={'last_name': '<PERSON>'}, input_type=dict]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/missing\n"]}], "source": ["missing_data = {\"last_name\": \"<PERSON>\"}\n", "\n", "try:\n", "    Person.model_validate(missing_data)\n", "except ValidationError as ex:\n", "    print(ex)"]}, {"cell_type": "markdown", "id": "330ac174", "metadata": {}, "source": ["The second data format that Pydantic can deserialize is JSON. This is of course hugely beneficial when working with REST APIs, where requests and responses are typically in JSON format.\n", "\n", "For that, Pydantic provides the model_validate_json() method."]}, {"cell_type": "code", "execution_count": 12, "id": "5e62613a", "metadata": {}, "outputs": [], "source": ["data_json = '''\n", "{\n", "    \"first_name\": \"<PERSON>\",\n", "    \"last_name\": \"<PERSON>\",\n", "    \"age\": 84,\n", "    \"city\": \"London\"\n", "}\n", "'''"]}, {"cell_type": "code", "execution_count": 13, "id": "96276f76", "metadata": {}, "outputs": [{"data": {"text/plain": ["Person(first_name='<PERSON>', last_name='<PERSON>', age=84, city='London')"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["p = Person.model_validate_json(data_json)\n", "p"]}, {"cell_type": "markdown", "id": "7ade0a08", "metadata": {}, "source": ["And validation exceptions happen just like before:"]}, {"cell_type": "code", "execution_count": 14, "id": "b1d92559", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3 validation errors for Person\n", "first_name\n", "  Field required [type=missing, input_value={'last_name': '<PERSON>'}, input_type=dict]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/missing\n", "age\n", "  Field required [type=missing, input_value={'last_name': '<PERSON>'}, input_type=dict]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/missing\n", "city\n", "  Field required [type=missing, input_value={'last_name': '<PERSON>'}, input_type=dict]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/missing\n"]}], "source": ["missing_data_json = '''\n", "{\n", "    \"last_name\": \"<PERSON>\"\n", "}\n", "'''\n", "\n", "try:\n", "    Person.model_validate_json(missing_data_json)\n", "except ValidationError as ex:\n", "    print(ex)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}