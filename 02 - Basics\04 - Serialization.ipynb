{"cells": [{"cell_type": "markdown", "id": "57108ca8", "metadata": {}, "source": ["### Serialization\n", "Let's start with the same model we had before:"]}, {"cell_type": "code", "execution_count": 1, "id": "1c136df4", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, ValidationError\n", "\n", "class Person(BaseModel):\n", "    first_name: str\n", "    last_name: str\n", "    age: int"]}, {"cell_type": "markdown", "id": "ee01d4f6", "metadata": {}, "source": ["And let's create a couple of instances of this model:"]}, {"cell_type": "code", "execution_count": 2, "id": "0c2538ad", "metadata": {}, "outputs": [], "source": ["galois = Person(first_name='<PERSON><PERSON><PERSON>', last_name='<PERSON><PERSON><PERSON>', age=20)\n", "newton = Person(first_name='<PERSON>', last_name='<PERSON>', age=84)"]}, {"cell_type": "markdown", "id": "eb422a48", "metadata": {}, "source": ["Those are standard Python objects, they even have instance dictionaries:"]}, {"cell_type": "code", "execution_count": 3, "id": "dd76ef98", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'first_name': '<PERSON>', 'last_name': '<PERSON>', 'age': 84}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["newton.__dict__"]}, {"cell_type": "markdown", "id": "b5e7c9c1", "metadata": {}, "source": ["But because they inherited from Pydantic's BaseModel, we have a lot of extra functionality.\n", "\n", "For example, we can choose to generate a dictionary, or a JSON string that represents the data in the instance.\n", "\n", "Pydantic provides us two methods for this:\n", "\n", "model_dump()\n", "model_dump_json()\n", "These methods will take the data in the instance, and produce a different object - a dict or a str.\n", "\n", "This is called serializing the model."]}, {"cell_type": "code", "execution_count": 4, "id": "45b9f176", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'first_name': '<PERSON><PERSON><PERSON>', 'last_name': '<PERSON><PERSON><PERSON>', 'age': 20}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["galois.model_dump()"]}, {"cell_type": "markdown", "id": "1ac93896", "metadata": {}, "source": ["This was a Python dict object:"]}, {"cell_type": "code", "execution_count": 5, "id": "b744276f", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["type(galois.model_dump())"]}, {"cell_type": "markdown", "id": "d072bba1", "metadata": {}, "source": ["And we can serialize to JSON:"]}, {"cell_type": "code", "execution_count": 6, "id": "1559c165", "metadata": {}, "outputs": [{"data": {"text/plain": ["'{\"first_name\":\"<PERSON>\",\"last_name\":\"<PERSON>\",\"age\":84}'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["newton.model_dump_json()"]}, {"cell_type": "markdown", "id": "37f5476b", "metadata": {}, "source": ["Notice the single quotes around the output above - that was a string, not a dictionary. And the string will contain valid JSON."]}, {"cell_type": "code", "execution_count": 7, "id": "0c6c63ea", "metadata": {}, "outputs": [{"data": {"text/plain": ["str"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["type(newton.model_dump_json())"]}, {"cell_type": "markdown", "id": "bf6a99ce", "metadata": {}, "source": ["Note that under the hood, Pydantic uses dumps() from the json module - which means you can technically pass arguments to it via the model_dump_json() method."]}, {"cell_type": "code", "execution_count": 8, "id": "d707d195", "metadata": {}, "outputs": [{"data": {"text/plain": ["'{\\n  \"first_name\": \"<PERSON>\",\\n  \"last_name\": \"<PERSON>\",\\n  \"age\": 84\\n}'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["newton.model_dump_json(indent=2)"]}, {"cell_type": "markdown", "id": "bc0ac44d", "metadata": {}, "source": ["And we can print() this so it handles escape characters properly for display:"]}, {"cell_type": "code", "execution_count": 9, "id": "6ea2f1db", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"first_name\": \"<PERSON>\",\n", "  \"last_name\": \"<PERSON>\",\n", "  \"age\": 84\n", "}\n"]}], "source": ["print(newton.model_dump_json(indent=2))"]}, {"cell_type": "markdown", "id": "62366617", "metadata": {}, "source": ["We can also choose whether to exclude certain fields from the serialization by using the exclude argument of the dump methods:"]}, {"cell_type": "code", "execution_count": 10, "id": "8f1743a4", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'last_name': '<PERSON><PERSON><PERSON>'}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["galois.model_dump(exclude=['first_name', 'age'])"]}, {"cell_type": "markdown", "id": "d49bfaa3", "metadata": {}, "source": ["Similarly with the JSON version:"]}, {"cell_type": "code", "execution_count": 11, "id": "800c4027", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'first_name': '<PERSON>', 'last_name': '<PERSON>'}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["newton.model_dump(exclude=['age'])"]}, {"cell_type": "markdown", "id": "edab30e7", "metadata": {}, "source": ["There are a number of different ways to control the data that gets serialized, and even how the data gets serialized. We'll explore this in later videos.\n", "\n", "Instead of picking which fields to exclude, we could also pick which fields to include (and it will then exclude all the others):"]}, {"cell_type": "code", "execution_count": 12, "id": "e6b09989", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'last_name': '<PERSON>'}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["newton.model_dump(include=[\"last_name\"])"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}