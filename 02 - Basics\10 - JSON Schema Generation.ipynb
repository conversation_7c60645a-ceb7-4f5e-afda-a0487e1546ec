{"cells": [{"cell_type": "markdown", "id": "54d37083", "metadata": {}, "source": ["### JSON Schema Generation"]}, {"cell_type": "code", "execution_count": 1, "id": "5e245c2d", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel"]}, {"cell_type": "code", "execution_count": 2, "id": "68892fea", "metadata": {}, "outputs": [], "source": ["class Model(BaseModel):\n", "    field_1: int | None = None\n", "    field_2: str = \"Python\""]}, {"cell_type": "code", "execution_count": 3, "id": "b489d9c3", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'properties': {'field_1': {'anyOf': [{'type': 'integer'}, {'type': 'null'}],\n", "   'default': None,\n", "   'title': 'Field 1'},\n", "  'field_2': {'default': 'Python', 'title': 'Field 2', 'type': 'string'}},\n", " 'title': 'Model',\n", " 'type': 'object'}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["Model.model_json_schema()"]}, {"cell_type": "code", "execution_count": 4, "id": "5c7ca821", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'properties': {'field_1': {'anyOf': [{'type': 'integer'}, {'type': 'null'}],\n", "                            'default': None,\n", "                            'title': 'Field 1'},\n", "                'field_2': {'default': 'Python',\n", "                            'title': 'Field 2',\n", "                            'type': 'string'}},\n", " 'title': 'Model',\n", " 'type': 'object'}\n"]}], "source": ["from pprint import pprint\n", "pprint(Model.model_json_schema())"]}, {"cell_type": "code", "execution_count": null, "id": "10a2f1ae", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}