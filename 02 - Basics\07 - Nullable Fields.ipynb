{"cells": [{"cell_type": "markdown", "id": "cdcacc3b", "metadata": {}, "source": ["## Nullable <PERSON>"]}, {"cell_type": "markdown", "id": "8e79bca0", "metadata": {}, "source": ["A nullable field is slightly different from an optional field.\n", "\n", "An optional field simply means that the data being deserialized does not need to contain the key for that specific field, and in that case, a pre-defined default is used.\n", "\n", "On the other hand, nullability of a field has nothing to do with whether it is optional or not - it basically just indicates whether a field can be set to None (or null in JSON) perspective.\n", "\n", "Let's take a look:\n", "\n", "Since Pydantic does type validation, if we define a field to be of some type, say int, trying to set that field to None will fail, since None is a distinct type in Python, and cannot be coerced to an int."]}, {"cell_type": "code", "execution_count": 2, "id": "33c136e8", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, ValidationError\n", "\n", "class Model(BaseModel):\n", "    field: int"]}, {"cell_type": "code", "execution_count": 3, "id": "c13baee9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 validation error for Model\n", "field\n", "  Input should be a valid integer [type=int_type, input_value=None, input_type=NoneType]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/int_type\n"]}], "source": ["try:\n", "    Model(field=None)\n", "except ValidationError as e:\n", "    print(e)"]}, {"cell_type": "markdown", "id": "4579a887", "metadata": {}, "source": ["As you can see, the exception we get here is saying that the data is not a valid integer, quite different from this:"]}, {"cell_type": "code", "execution_count": 4, "id": "831df50e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 validation error for Model\n", "field\n", "  Field required [type=missing, input_value={}, input_type=dict]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/missing\n"]}], "source": ["try:\n", "    Model()\n", "except ValidationError as e:\n", "    print(e)"]}, {"cell_type": "markdown", "id": "aa5132d1", "metadata": {}, "source": ["where the exception has to do with a required field.\n", "\n", "To indicate to Pydantic that a field is nullable (will entertain either the actual type, or the None type), we need to specify it in the type hint."]}, {"cell_type": "code", "execution_count": 5, "id": "1c91b576", "metadata": {}, "outputs": [], "source": ["class Model(BaseModel):\n", "    field: int | None"]}, {"cell_type": "markdown", "id": "6ee9d832", "metadata": {}, "source": ["This type hint informs Pydantic that either an integer (or something that can be coerced to an integer) or None are acceptabler values for that field."]}, {"cell_type": "code", "execution_count": 6, "id": "66dfd76b", "metadata": {}, "outputs": [{"data": {"text/plain": ["Model(field=None)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["Model(field=None)"]}, {"cell_type": "code", "execution_count": 7, "id": "d347c883", "metadata": {}, "outputs": [{"data": {"text/plain": ["Model(field=1)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["Model(field=\"1\")"]}, {"cell_type": "markdown", "id": "0ec0aaa4", "metadata": {}, "source": ["As you can see, we now have a nullable field.\n", "\n", "But the field is not optional!"]}, {"cell_type": "code", "execution_count": 8, "id": "8f2392aa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 validation error for Model\n", "field\n", "  Field required [type=missing, input_value={}, input_type=dict]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/missing\n"]}], "source": ["try:\n", "    Model()\n", "except ValidationError as e:\n", "    print(e)"]}, {"cell_type": "markdown", "id": "316fa9e1", "metadata": {}, "source": ["Nullable fields and optional fields often go hand in hand, simply because we often choose to set the default for a field to None, to indicate no value has been provided in the data.\n", "\n", "In a case like this, we would the field to be both nullable and optional.\n", "\n", "We do it this way:"]}, {"cell_type": "code", "execution_count": 9, "id": "314b9a65", "metadata": {}, "outputs": [], "source": ["class Model(BaseModel):\n", "    field: int | None = None"]}, {"cell_type": "code", "execution_count": 10, "id": "749b4bd3", "metadata": {}, "outputs": [{"data": {"text/plain": ["Model(field=None)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["Model()"]}, {"cell_type": "code", "execution_count": 11, "id": "a49c34bf", "metadata": {}, "outputs": [{"data": {"text/plain": ["Model(field=None)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["Model(field=None)"]}, {"cell_type": "code", "execution_count": 12, "id": "02e04fe2", "metadata": {}, "outputs": [{"data": {"text/plain": ["Model(field=1)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["Model(field=\"1\")"]}, {"cell_type": "markdown", "id": "39d0f2d6", "metadata": {}, "source": ["Now we have a field that is optional (because a default has been provided), and is nullable (because of the int | None type hint).\n", "\n", "We just happened to choose <PERSON> as a default value, but of course it could be something any integer value too.\n", "\n", "I want to point out that the notation int | None only became available in Python 3.10.\n", "\n", "Before that, we had (and still have) two additional ways of specifying this type hint.\n", "\n", "The first one is by using <PERSON> from the typing module:"]}, {"cell_type": "code", "execution_count": 26, "id": "c2950c1d", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'field': FieldInfo(annotation=Union[int, NoneType], required=True)}"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import Union\n", "\n", "class Model(BaseModel):\n", "    field: Union[int, None]\n", "    \n", "Model.model_fields    "]}, {"cell_type": "markdown", "id": "440a3e20", "metadata": {}, "source": ["This is completely equivalent to writing int | None."]}, {"cell_type": "code", "execution_count": 27, "id": "d8893c2b", "metadata": {}, "outputs": [{"data": {"text/plain": ["Model(field=None)"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["Model(field=None)"]}, {"cell_type": "code", "execution_count": 28, "id": "57a75212", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'field': FieldInfo(annotation=Union[int, NoneType], required=False, default=None)}"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import Union\n", "class Model(BaseModel):\n", "    field: Union[int, None] = None\n", "    \n", "Model.model_fields"]}, {"cell_type": "code", "execution_count": null, "id": "b60eea65", "metadata": {}, "outputs": [], "source": ["# When you set a default value of None for a field (e.g., field: int | None = None),\n", "# Pydantic treats the field as optional (required=False) because a value is always available (the default).\n", "# If no default is provided, the field is required (required=True).\n", "# This is why adding `= None` makes the model field not required.\n", "\n", "print(Model.model_fields['field'].required)\n", "print(Model.model_fields['field'].default)"]}, {"cell_type": "markdown", "id": "921f014c", "metadata": {}, "source": ["Yet another way is to use Optional, also from the typing module:"]}, {"cell_type": "code", "execution_count": 29, "id": "fe284075", "metadata": {}, "outputs": [], "source": ["from typing import Optional\n", "\n", "class Model(BaseModel):\n", "    field: Optional[int]"]}, {"cell_type": "markdown", "id": "6fb794cc", "metadata": {}, "source": ["I typically do not use Optional when working with Python versions prior to 3.10.\n", "\n", "The reason I do not like it, is that when working with Pydantic models, optional means something else. Here, we use Optional to indicate that field is nullable, not to indicate that it is optional.\n", "\n", "We can verify this:"]}, {"cell_type": "code", "execution_count": 30, "id": "a81efc27", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 validation error for Model\n", "field\n", "  Field required [type=missing, input_value={}, input_type=dict]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/missing\n"]}], "source": ["try:\n", "    Model()\n", "except ValidationError as ex:\n", "    print(ex)"]}, {"cell_type": "markdown", "id": "20510e93", "metadata": {}, "source": ["In order to make a field optional we have to provide a default value, like this:"]}, {"cell_type": "code", "execution_count": 31, "id": "8a4533aa", "metadata": {}, "outputs": [], "source": ["class Model(BaseModel):\n", "    field: Optional[int] = None"]}, {"cell_type": "code", "execution_count": 32, "id": "7ff25473", "metadata": {}, "outputs": [{"data": {"text/plain": ["Model(field=None)"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["Model()"]}, {"cell_type": "markdown", "id": "959718af", "metadata": {}, "source": ["Those three ways of specifying nullability are completely equivalent:"]}, {"cell_type": "code", "execution_count": 33, "id": "2fd25407", "metadata": {}, "outputs": [], "source": ["class Model(BaseModel):\n", "    field_1: int | None\n", "    field_2: Union[int, None]\n", "    field_3: Optional[int]"]}, {"cell_type": "markdown", "id": "2a26710f", "metadata": {}, "source": ["And, in fact, when we look at the field definitions:"]}, {"cell_type": "code", "execution_count": 34, "id": "70299a94", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'field_1': FieldInfo(annotation=Union[int, NoneType], required=True),\n", " 'field_2': FieldInfo(annotation=Union[int, NoneType], required=True),\n", " 'field_3': FieldInfo(annotation=Union[int, NoneType], required=True)}"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["Model.model_fields"]}, {"cell_type": "markdown", "id": "a00d7fed", "metadata": {}, "source": ["ou'll notice that all three fields are represented by Pydantic as Union[int, None]\n", "\n", "Both int | None and Optional[int] are just convenience syntax to represent Union[int, None].\n", "\n", "A very common mistake beginners make, is to set a field default to None without indicating that the field is nullable.\n", "\n", "In other words I often see code like this:"]}, {"cell_type": "code", "execution_count": 35, "id": "10e35d56", "metadata": {}, "outputs": [], "source": ["class Model(BaseModel):\n", "    field: int = None"]}, {"cell_type": "markdown", "id": "ab3c947b", "metadata": {}, "source": ["Because of the way Pydantic does not validate defaults (by default), the above model will appear to work fine, but could cause trouble down the road:"]}, {"cell_type": "code", "execution_count": 36, "id": "11500bd1", "metadata": {}, "outputs": [{"data": {"text/plain": ["Model(field=None)"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["m = Model()\n", "m"]}, {"cell_type": "markdown", "id": "63c0d32e", "metadata": {}, "source": ["But of course the field is not really nullable, so this would not work:"]}, {"cell_type": "code", "execution_count": 37, "id": "11841a05", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 validation error for Model\n", "field\n", "  Input should be a valid integer [type=int_type, input_value=None, input_type=NoneType]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/int_type\n"]}], "source": ["try:\n", "    Model(field=None)\n", "except ValidationError as ex:\n", "    print(ex)"]}, {"cell_type": "markdown", "id": "94bb1527", "metadata": {}, "source": ["The correct way to define this model would be:"]}, {"cell_type": "code", "execution_count": 38, "id": "976372d8", "metadata": {}, "outputs": [], "source": ["class Model(BaseModel):\n", "    field: int | None = None"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}