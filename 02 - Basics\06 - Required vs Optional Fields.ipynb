{"cells": [{"cell_type": "markdown", "id": "52388348", "metadata": {}, "source": ["## Required vs Optional Fields"]}, {"cell_type": "markdown", "id": "4f3d75f3", "metadata": {}, "source": ["So far, all the fields we defined in our Pydantic model were required.\n", "As an analogy, how do we make function arguments optional in Python?\n", "We provide the argument a default value.\n", "The same approach is used by Pydantic.\n", "We make a field optional, by simply providing the field definition with a default value.\n", "There are a few ways of doing this, we'll explore one way here - and explore other ways later in the course."]}, {"cell_type": "code", "execution_count": 14, "id": "d7486dfd", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel"]}, {"cell_type": "code", "execution_count": 15, "id": "3edc1d67", "metadata": {}, "outputs": [], "source": ["class Circle(BaseModel):\n", "    center: tuple[int, int]\n", "    radius: float   "]}, {"cell_type": "code", "execution_count": 16, "id": "a1ffb6c8", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'center': FieldInfo(annotation=tuple[int, int], required=True),\n", " 'radius': FieldInfo(annotation=float, required=True)}"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["Circle.model_fields"]}, {"cell_type": "code", "execution_count": 17, "id": "3bf43870", "metadata": {}, "outputs": [], "source": ["class Circle(BaseModel):\n", "    center: tuple[int, int]=(0, 0)\n", "    radius: int  "]}, {"cell_type": "markdown", "id": "ea2c04b9", "metadata": {}, "source": ["In this model, center is an optional field, that will default to (0, 0) if not provided in the data we are deserializing. on the other hand, since radius does not have a default defined, it is a required field.\n", "\n", "We can also see this by inspecting the model fields:"]}, {"cell_type": "code", "execution_count": 18, "id": "77b25323", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'center': FieldInfo(annotation=tuple[int, int], required=False, default=(0, 0)),\n", " 'radius': FieldInfo(annotation=int, required=True)}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["Circle.model_fields"]}, {"cell_type": "markdown", "id": "201fb2b1", "metadata": {}, "source": ["Note how center has the required=False property, while radius has required=True.\n", "\n", "We can now create instances of Circle without providing a value for center:"]}, {"cell_type": "code", "execution_count": 19, "id": "96ddcfea", "metadata": {}, "outputs": [{"data": {"text/plain": ["Circle(center=(0, 0), radius=1)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["Circle(radius=1)"]}, {"cell_type": "markdown", "id": "e9442733", "metadata": {}, "source": ["As you can see, no validation exception was raised, and we have the proper default value in place.\n", "\n", "This works the same way for all the other deserializtion methods too:"]}, {"cell_type": "code", "execution_count": 20, "id": "6b206930", "metadata": {}, "outputs": [], "source": ["data = {\"radius\": 1}\n", "data_json = '{ \"radius\": 1}'"]}, {"cell_type": "code", "execution_count": 21, "id": "69a45bd6", "metadata": {}, "outputs": [{"data": {"text/plain": ["Circle(center=(0, 0), radius=1)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["Circle.model_validate(data)"]}, {"cell_type": "code", "execution_count": 22, "id": "3fa46c35", "metadata": {}, "outputs": [{"data": {"text/plain": ["Circle(center=(0, 0), radius=1)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["Circle.model_validate_json(data_json)"]}, {"cell_type": "markdown", "id": "2f90c710", "metadata": {}, "source": ["Of course, we can provide a value for center too:"]}, {"cell_type": "code", "execution_count": 23, "id": "deb946f2", "metadata": {}, "outputs": [{"data": {"text/plain": ["Circle(center=(1, 1), radius=2)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["Circle(center=(1, 1), radius=2)"]}, {"cell_type": "markdown", "id": "085bdea0", "metadata": {}, "source": ["We have to be quite careful about one thing when specifying a default value for a field.\n", "\n", "When we provide a field value, Pydantic validates that value before putting it into our model instance. However, Pydantic's default behavior does not validate default values!\n", "\n", "That kind of makes sense - after all, we are writing the model definition, so we should be able to only provide a valid default value in our model definition.\n", "\n", "So, here's the issue:"]}, {"cell_type": "code", "execution_count": 24, "id": "93ef6a9e", "metadata": {}, "outputs": [], "source": ["class Model(BaseModel):\n", "    field: int = \"Python\""]}, {"cell_type": "markdown", "id": "d76c0fc3", "metadata": {}, "source": ["As you can see, the provided default is totally inconsistent for an int type, and yet this still works:"]}, {"cell_type": "code", "execution_count": 25, "id": "e9ada694", "metadata": {}, "outputs": [{"data": {"text/plain": ["Model(field='Python')"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["Model()"]}, {"cell_type": "markdown", "id": "ea0d5242", "metadata": {}, "source": ["So, be careful when providing default values, the onus is on us, as developers, to make sure we provided consistent defaults.\n", "\n", "Pydantic does offer us a way to force default data validations - but it does mean a little of extra compute time to validate that default value every time it is needed. Something we can avoid if we write correct code. We'll see later how to enable default validations on a model.\n", "\n", "Usually in Python, including dataclasses, we have to be extra careful when we assign a default that is a mutable object.\n", "\n", "let's look at an example of this with a regular Python function:\n", "\n", "By the way, writing a function that behaves this way - modifies it's input and returns it, is a terrible coding technique - I'm just using this to illustrate a point.\n", "\n", "We can call this function with our own list:"]}, {"cell_type": "code", "execution_count": 26, "id": "744869fb", "metadata": {}, "outputs": [], "source": ["from time import time\n", "\n", "def extend_list(user_list: list = []):\n", "    user_list.append(int(time()))\n", "    return user_list"]}, {"cell_type": "code", "execution_count": 27, "id": "a8cf6a8d", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1747879467]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["my_times = []\n", "extend_list(my_times)\n", "my_times"]}, {"cell_type": "code", "execution_count": 28, "id": "dfcde5ad", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1747879478]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["my_times = extend_list()\n", "my_times"]}, {"cell_type": "markdown", "id": "87d21dee", "metadata": {}, "source": ["And this seems to have worked.\n", "\n", "But what about this?"]}, {"cell_type": "code", "execution_count": 29, "id": "53f34984", "metadata": {}, "outputs": [], "source": ["my_new_times = extend_list()"]}, {"cell_type": "markdown", "id": "c0405508", "metadata": {}, "source": ["Our expectation might be that my_new_times just contains one element, whatever the epoch time was when we called extend_list()."]}, {"cell_type": "code", "execution_count": 30, "id": "2864bfde", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1747879478, 1747879509]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["my_new_times"]}, {"cell_type": "markdown", "id": "a9af82b6", "metadata": {}, "source": ["This is the issue I was telling you about. When we defined a default for function arguments, these values are calculated and stored with the function itself - they are not re-created every time the function is called. In other words, that default value is going to be shared amogst all the function calls.\n", "\n", "Something similar happens with dataclases. In dataclasses, we get around the problem by a mechanism that is called a default factory. Instead of providing a mutable object as a default value, we provide a function that will get called each time a dataclass instance is created, and that function will therefore return a new default object.\n", "\n", "Pydantic is very similar, in that it offers this default factory idea (which we'll cover later).\n", "\n", "However, it goes one step further, and actually allows us to simply define mutable objects as defaults.\n", "\n", "When Pydantic sees this, it will actually create a deep copy of the mutable object every time a new model instance is created."]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}