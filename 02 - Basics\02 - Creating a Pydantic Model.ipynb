{"cells": [{"cell_type": "markdown", "id": "a27471e5", "metadata": {}, "source": ["Creating a Pydantic Model\n", "https://github.com/fbaptiste/pydantic-essentials"]}, {"cell_type": "code", "execution_count": 1, "id": "eea9197e", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel"]}, {"cell_type": "code", "execution_count": 2, "id": "63401802", "metadata": {}, "outputs": [], "source": ["class Person(BaseModel):\n", "    first_name: str\n", "    last_name: str\n", "    age: int\n", "    city: str"]}, {"cell_type": "code", "execution_count": 3, "id": "84266e2c", "metadata": {}, "outputs": [], "source": ["p = Person(first_name=\"<PERSON>\", last_name=\"<PERSON><PERSON>\", age=30, city=\"New York\")\n"]}, {"cell_type": "code", "execution_count": 4, "id": "5e3d964b", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"first_name='<PERSON>' last_name='<PERSON><PERSON>' age=30 city='New York'\""]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["str(p)"]}, {"cell_type": "code", "execution_count": 5, "id": "d0294c3b", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Person(first_name='<PERSON>', last_name='<PERSON><PERSON>', age=30, city='New York')\""]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["repr(p)"]}, {"cell_type": "code", "execution_count": 6, "id": "dc3b7c91", "metadata": {}, "outputs": [{"data": {"text/plain": ["Person(first_name='<PERSON>', last_name='<PERSON><PERSON>', age=30, city='New York')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["p"]}, {"cell_type": "code", "execution_count": 7, "id": "885e8948", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Documents\\Wondershare\\CreatorTemp\\ipykernel_27196\\1166466245.py:1: PydanticDeprecatedSince211: Accessing the 'model_fields' attribute on the instance is deprecated. Instead, you should access this attribute from the model class. Deprecated in Pydantic V2.11 to be removed in V3.0.\n", "  p.model_fields\n"]}, {"data": {"text/plain": ["{'first_name': FieldInfo(annotation=str, required=True),\n", " 'last_name': FieldInfo(annotation=str, required=True),\n", " 'age': FieldInfo(annotation=int, required=True),\n", " 'city': FieldInfo(annotation=str, required=True)}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["p.model_fields"]}, {"cell_type": "code", "execution_count": 8, "id": "a0b27267", "metadata": {}, "outputs": [], "source": ["from pydantic import ValidationError"]}, {"cell_type": "code", "execution_count": 10, "id": "16292e12", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2 validation errors for Person\n", "first_name\n", "  Field required [type=missing, input_value={'last_name': '<PERSON><PERSON>', 'age...ty', 'city': 'New York'}, input_type=dict]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/missing\n", "age\n", "  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='thirty', input_type=str]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/int_parsing\n"]}], "source": ["try:\n", "    Person(last_name=\"<PERSON><PERSON>\", age=\"thirty\", city=\"New York\")\n", "except ValidationError as e:\n", "    print(e)"]}, {"cell_type": "code", "execution_count": 11, "id": "c69825bc", "metadata": {}, "outputs": [], "source": ["class Person(BaseModel):\n", "    first_name: str\n", "    last_name: str\n", "    age: int\n", "    city: str\n", "    \n", "    @property\n", "    def display_name(self):\n", "        return f\"{self.first_name} {self.last_name}\""]}, {"cell_type": "code", "execution_count": 12, "id": "7851964e", "metadata": {}, "outputs": [], "source": ["p = Person(first_name=\"<PERSON><PERSON><PERSON>\", last_name=\"<PERSON><PERSON>\", age=45, city=\"Toronto\")"]}, {"cell_type": "code", "execution_count": 13, "id": "eb358f7b", "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON><PERSON><PERSON>'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["p.display_name"]}, {"cell_type": "code", "execution_count": 14, "id": "fa4be36a", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Sanjiv'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["p.first_name"]}, {"cell_type": "code", "execution_count": 16, "id": "28b696ce", "metadata": {}, "outputs": [], "source": ["p = Person(first_name=\"<PERSON><PERSON><PERSON>\", last_name=\"<PERSON><PERSON>\", age=45, city=\"Toronto\")"]}, {"cell_type": "code", "execution_count": 17, "id": "f2d5ed0e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 validation error for Person\n", "age\n", "  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Forty Five', input_type=str]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/int_parsing\n"]}], "source": ["try:\n", "    Person(first_name=\"<PERSON><PERSON><PERSON>\", last_name=\"<PERSON><PERSON>\", age=\"Forty Five\", city=\"Toronto\")\n", "except Exception as e:\n", "    print(e)"]}, {"cell_type": "code", "execution_count": 18, "id": "c4946d59", "metadata": {}, "outputs": [{"data": {"text/plain": ["Person(first_name='<PERSON><PERSON><PERSON>', last_name='<PERSON><PERSON>', age=45, city='Toronto')"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["p"]}, {"cell_type": "code", "execution_count": 19, "id": "720d92c8", "metadata": {}, "outputs": [{"data": {"text/plain": ["45"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["p.age"]}, {"cell_type": "code", "execution_count": 20, "id": "e98fb5f8", "metadata": {}, "outputs": [], "source": ["p.age = \"Forty Five\""]}, {"cell_type": "code", "execution_count": 21, "id": "94652c45", "metadata": {}, "outputs": [{"data": {"text/plain": ["Person(first_name='<PERSON><PERSON><PERSON>', last_name='<PERSON><PERSON>', age='Forty Five', city='Toronto')"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["p"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}