{"cells": [{"cell_type": "markdown", "id": "35cb8ac9", "metadata": {}, "source": ["## Combining Nullable and Optional"]}, {"cell_type": "markdown", "id": "1058a824", "metadata": {}, "source": ["Since fields can be:\n", "\n", "- nullable or not nullable\n", "- required or optional\n", "we have four different ways to combine these attributes.\n", "\n", "This may sound rather obvious, but it can sometimes lead to confusion, so let's look at each combination separately, and make sure we understand what it means in terms of validation."]}, {"cell_type": "markdown", "id": "c5cab882", "metadata": {}, "source": ["### Required, Not Nullable"]}, {"cell_type": "markdown", "id": "65593735", "metadata": {}, "source": ["This is basically the default way of defining fields:"]}, {"cell_type": "code", "execution_count": 1, "id": "4f755157", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, ValidationError"]}, {"cell_type": "code", "execution_count": 2, "id": "35a28473", "metadata": {}, "outputs": [], "source": ["class Model(BaseModel):\n", "    field: int "]}, {"cell_type": "markdown", "id": "f56142d4", "metadata": {}, "source": ["In this case field is required, and it only allows for integers (hence not nullable)."]}, {"cell_type": "code", "execution_count": 3, "id": "3b25ec1f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 validation error for Model\n", "field\n", "  Field required [type=missing, input_value={}, input_type=dict]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/missing\n"]}], "source": ["try:\n", "    Model()\n", "except ValidationError as e:\n", "    print(e)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "e2c2d126", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 validation error for Model\n", "field\n", "  Input should be a valid integer [type=int_type, input_value=None, input_type=NoneType]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/int_type\n"]}], "source": ["try:\n", "    Model(field=None)\n", "except ValidationError as e:\n", "    print(e)"]}, {"cell_type": "markdown", "id": "45790370", "metadata": {}, "source": ["### Required, Nullable"]}, {"cell_type": "code", "execution_count": 5, "id": "5646af88", "metadata": {}, "outputs": [], "source": ["class Model(BaseModel):\n", "    field: int | None"]}, {"cell_type": "code", "execution_count": 6, "id": "0336d3b9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 validation error for Model\n", "field\n", "  Field required [type=missing, input_value={}, input_type=dict]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/missing\n"]}], "source": ["try:\n", "    Model()\n", "except ValidationError as e:\n", "    print(e)"]}, {"cell_type": "code", "execution_count": 7, "id": "18714fb0", "metadata": {}, "outputs": [{"data": {"text/plain": ["Model(field=None)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["Model(field=None)"]}, {"cell_type": "markdown", "id": "b9cb95ab", "metadata": {}, "source": ["### Optional, Not Nullable"]}, {"cell_type": "code", "execution_count": 10, "id": "07c66354", "metadata": {}, "outputs": [], "source": ["class Model(BaseModel):\n", "    field: int = 0"]}, {"cell_type": "markdown", "id": "ac67ee5a", "metadata": {}, "source": ["As I pointed out in an earlier video, be careful not to specify None as the default, since the type hint does not indicate that None is acceptable. By default Pydantic will allow it (since it does not validate defaults), but in an upcoming video we'll see how to change that behavior."]}, {"cell_type": "code", "execution_count": 12, "id": "0275b341", "metadata": {}, "outputs": [{"data": {"text/plain": ["Model(field=0)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["Model()"]}, {"cell_type": "code", "execution_count": 13, "id": "8b0b0f9c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 validation error for Model\n", "field\n", "  Input should be a valid integer [type=int_type, input_value=None, input_type=NoneType]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/int_type\n"]}], "source": ["try:\n", "    Model(field=None)\n", "except ValidationError as e:\n", "    print(e)"]}, {"cell_type": "markdown", "id": "65a18502", "metadata": {}, "source": ["### Optional, Nullable"]}, {"cell_type": "code", "execution_count": 14, "id": "dcdb4efe", "metadata": {}, "outputs": [], "source": ["class Model(BaseModel):\n", "    field: int | None = 0"]}, {"cell_type": "code", "execution_count": 15, "id": "7d70404a", "metadata": {}, "outputs": [{"data": {"text/plain": ["Model(field=0)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["Model()"]}, {"cell_type": "code", "execution_count": 17, "id": "815e8619", "metadata": {}, "outputs": [{"data": {"text/plain": ["Model(field=None)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["Model(field=None)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}