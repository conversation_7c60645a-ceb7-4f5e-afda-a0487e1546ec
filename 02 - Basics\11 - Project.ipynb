{"cells": [{"cell_type": "markdown", "id": "37f9ac88", "metadata": {}, "source": ["### Project Section 2"]}, {"cell_type": "markdown", "id": "112fa9c7", "metadata": {}, "source": ["We are going to build a model for an automobile.\n", "\n", "Throughout the course, at the end of each section you will add on to this model, refactor some parts of it, or create related models that will then be used in conjunction with this automobile model when we get to model composition.\n", "\n", "To start, you should create an Automobile model that contains the following fields:\n", "\n", "- manufacturer, string, required, not nullable\n", "- series_name, string, required, not nullable\n", "- type_, string, required, not nullable\n", "- is_electric, boolean, defaults to False, not nullable\n", "- manufactured_date, date, required (hint use date from datetime module as your field type hint),not nullable\n", "- base_msrp_usd, float, required, not nullable\n", "- vin, string, required, not nullable\n", "- number_of_doors, integer, defaults to 4, not nullable\n", "- registration_country, string, defaults to None\n", "- license_plate, string, defaults to None\n", "Once you have created your model, you should test deserializing and serializing your model and make sure everything works.\n", "\n", "To help you, in most sections, I provide some sample \"input\" and \"output\" data that you can use to check your model is working.\n", "\n", "You can test your model by deserializing the following input data, and comparing the serialization of each of those models to the provided Python dictionaries.\n", "\n", "In other words, test them by doing something like this for both cases:\n", "\n", "create model by deserializing the data\n", "check the model's serialization to dict is equal to the provided expected dictionary"]}, {"cell_type": "code", "execution_count": 1, "id": "1a33d465", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, ValidationError"]}, {"cell_type": "code", "execution_count": 15, "id": "795025cd", "metadata": {}, "outputs": [], "source": ["from datetime import date\n", "class Automobile(BaseModel):\n", "    manufacturer: str\n", "    series_name: str\n", "    type_: str\n", "    is_electric: bool = False\n", "    manufactured_date: date\n", "    base_msrp_usd: float\n", "    vin: str\n", "    number_of_doors: int = 4\n", "    registration_country: str | None = None\n", "    license_plate: str | None = None"]}, {"cell_type": "code", "execution_count": 30, "id": "485486c0", "metadata": {}, "outputs": [], "source": ["#Python dictionary\n", "\n", "data = {\n", "    \"manufacturer\": \"Tesla\",\n", "    \"series_name\": \"Model S\",\n", "    \"type_\": \"Sedan\",\n", "    \"is_electric\": True,\n", "    \"manufactured_date\": date(2020, 1, 1),\n", "    \"base_msrp_usd\": 35000,\n", "    \"vin\": \"12345678901234567\",\n", "    \"number_of_doors\": 4,\n", "    \"registration_country\": \"USA\",\n", "    \"license_plate\": \"ABC123\",\n", "}\n", "\n", "data_expected_serialization ={\n", "    \"manufacturer\": \"Tesla\",\n", "    \"series_name\": \"Model S\",\n", "    \"type_\": \"Sedan\",\n", "    \"is_electric\": True,\n", "    \"manufactured_date\": date(2020, 1, 1),\n", "    \"base_msrp_usd\": 35000,\n", "    \"vin\": \"12345678901234567\",\n", "    \"number_of_doors\": 4,\n", "    \"registration_country\": \"USA\",\n", "    \"license_plate\": \"ABC123\",\n", "}\n"]}, {"cell_type": "code", "execution_count": 31, "id": "75b6e677", "metadata": {}, "outputs": [{"data": {"text/plain": ["Automobile(manufacturer='Tesla', series_name='Model S', type_='Sedan', is_electric=True, manufactured_date=datetime.date(2020, 1, 1), base_msrp_usd=35000.0, vin='12345678901234567', number_of_doors=4, registration_country='USA', license_plate='ABC123')"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["car = Automobile.model_validate(data)\n", "car"]}, {"cell_type": "code", "execution_count": 32, "id": "4cf8bde0", "metadata": {}, "outputs": [], "source": ["assert car.model_dump() == data_expected_serialization, \"Serialization failed\""]}, {"cell_type": "code", "execution_count": 62, "id": "cbae04ae", "metadata": {}, "outputs": [], "source": ["data_json = ''' \n", "{\n", "    \"manufacturer\": \"Tesla\",\n", "    \"series_name\": \"Model 3\",\n", "    \"type_\": \"Sedan\",\n", "    \"manufactured_date\": \"2020-01-01\",\n", "    \"base_msrp_usd\": 35000,\n", "    \"vin\": \"12345678901234567\"\n", "}\n", "'''\n", "data_json_expected_serialization = {\n", "    \"manufacturer\": \"Tesla\",\n", "    \"series_name\": \"Model 3\",\n", "    \"type_\": \"Sedan\",\n", "    \"is_electric\": <PERSON><PERSON><PERSON>,\n", "    \"manufactured_date\": date(2020, 1, 1),\n", "    \"base_msrp_usd\": 35000.0,\n", "    \"vin\": \"12345678901234567\",\n", "    'number_of_doors': 4,\n", "    \"registration_country\": None,\n", "    \"license_plate\": None\n", "}"]}, {"cell_type": "code", "execution_count": 63, "id": "0cae91ac", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'manufacturer': 'Tesla',\n", " 'series_name': 'Model 3',\n", " 'type_': '<PERSON><PERSON>',\n", " 'is_electric': <PERSON><PERSON><PERSON>,\n", " 'manufactured_date': datetime.date(2020, 1, 1),\n", " 'base_msrp_usd': 35000.0,\n", " 'vin': '12345678901234567',\n", " 'number_of_doors': 4,\n", " 'registration_country': None,\n", " 'license_plate': None}"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["car_json = Automobile.model_validate_json(data_json)\n", "car_json.model_dump()\n"]}, {"cell_type": "code", "execution_count": 64, "id": "d9aef5ef", "metadata": {}, "outputs": [], "source": ["car_json = Automobile.model_validate_json(data_json)\n", "car_json\n", "\n", "assert car_json.model_dump() == data_json_expected_serialization, \"Serialization failed\""]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}