{"cells": [{"cell_type": "markdown", "id": "c27ff917", "metadata": {}, "source": ["## Inspecting Fields"]}, {"cell_type": "code", "execution_count": 2, "id": "6a39f8ee", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, ValidationError"]}, {"cell_type": "code", "execution_count": 3, "id": "7fccdc5e", "metadata": {}, "outputs": [], "source": ["class Circle(BaseModel):\n", "    center_x: int = 0\n", "    center_y: int = 0\n", "    radius: int = 1\n", "    name: str | None = None"]}, {"cell_type": "code", "execution_count": 4, "id": "70a647f6", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'center_x': FieldInfo(annotation=int, required=False, default=0),\n", " 'center_y': FieldInfo(annotation=int, required=False, default=0),\n", " 'radius': FieldInfo(annotation=int, required=False, default=1),\n", " 'name': FieldInfo(annotation=Union[str, NoneType], required=False, default=None)}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["Circle.model_fields"]}, {"cell_type": "code", "execution_count": 13, "id": "8267d97b", "metadata": {}, "outputs": [], "source": ["c1 = Circle(radius=2)\n", "c2 = Circle(name='unit circle')\n", "c3 = Circle(radius = 3, name='large circle')"]}, {"cell_type": "code", "execution_count": 6, "id": "8c133f1b", "metadata": {}, "outputs": [{"data": {"text/plain": ["Circle(center_x=0, center_y=0, radius=2, name=None)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["c1"]}, {"cell_type": "code", "execution_count": 7, "id": "3d98f3d8", "metadata": {}, "outputs": [{"data": {"text/plain": ["Circle(center_x=0, center_y=0, radius=1, name='unit circle')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["c2"]}, {"cell_type": "code", "execution_count": 8, "id": "3488c2fc", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'center_x': 0, 'center_y': 0, 'radius': 2, 'name': None}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["c1.model_dump()"]}, {"cell_type": "code", "execution_count": 11, "id": "3ab16607", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'radius'}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["c1.model_fields_set"]}, {"cell_type": "code", "execution_count": 12, "id": "2e21f1c2", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'name'}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["c2.model_fields_set"]}, {"cell_type": "code", "execution_count": 14, "id": "f25989c5", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'name', 'radius'}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["c3.model_fields_set"]}, {"cell_type": "code", "execution_count": 17, "id": "516c7d8c", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'center_x', 'center_y', 'name'}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["Circle.model_fields.keys() - c1.model_fields_set"]}, {"cell_type": "markdown", "id": "fb4d6811", "metadata": {}, "source": ["Getting the model fields keys from the instance method is depricated so I am using the class method instead."]}, {"cell_type": "code", "execution_count": 20, "id": "99554444", "metadata": {}, "outputs": [], "source": ["class Model(BaseModel):\n", "    field_1: int = 1  # required and non-nullable\n", "    field_2: int | None = None  # optional and nullable\n", "    field_3: str   # required and non-nullable\n", "    field_4: str | None = \"Python\"  # optional and nullable\n"]}, {"cell_type": "code", "execution_count": 21, "id": "88d3b923", "metadata": {}, "outputs": [], "source": ["m1 = Model(field_3 = \"m1\")\n", "m2 = Model(field_1 = 1, field_2 = None, field_3 = \"m2\", field_4 = \"Python\")\n", "m3 = Model(field_1 = 10, field_2 = 20, field_3 = \"m3\", field_4 = \"Pydantic\")"]}, {"cell_type": "code", "execution_count": 22, "id": "8de16e51", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'field_1': 1, 'field_2': None, 'field_3': 'm1', 'field_4': 'Python'}"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["m1.model_dump()"]}, {"cell_type": "code", "execution_count": 23, "id": "a462ad03", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'field_3'}"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["m1.model_fields_set"]}, {"cell_type": "code", "execution_count": 24, "id": "e43fa9cb", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'field_3': 'm1'}"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["m1.model_dump(include = m1.model_fields_set)\n"]}, {"cell_type": "code", "execution_count": 25, "id": "ef6e6edb", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'field_1': 1, 'field_2': None, 'field_3': 'm2', 'field_4': 'Python'}"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["m2.model_dump(include = m2.model_fields_set)"]}, {"cell_type": "code", "execution_count": null, "id": "f10ff8b6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}