{"cells": [{"cell_type": "markdown", "id": "9bad9602", "metadata": {}, "source": ["### Type Coercion\n", "When Pydantic deserializes data, one of the things it does is perform validation. This includes ensuring that the model instance data ends up as the correct type.\n", "\n", "Let's look at an example:"]}, {"cell_type": "code", "execution_count": 1, "id": "87afd0a3", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, ValidationError\n", "class Coordinates(BaseModel):\n", "    x: float\n", "    y: float"]}, {"cell_type": "markdown", "id": "1c96208d", "metadata": {}, "source": ["Notice how we are insisting that x and y should be floats.\n", "\n", "Let's deseralize some data:"]}, {"cell_type": "code", "execution_count": 2, "id": "1288d074", "metadata": {}, "outputs": [{"data": {"text/plain": ["Coordinates(x=1.1, y=2.2)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["p1 = Coordinates(x=1.1, y=2.2)\n", "p1"]}, {"cell_type": "markdown", "id": "17cb2d25", "metadata": {}, "source": ["We can see our field definitions:"]}, {"cell_type": "code", "execution_count": 3, "id": "49f1101b", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'x': FieldInfo(annotation=float, required=True),\n", " 'y': FieldInfo(annotation=float, required=True)}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["Coordinates.model_fields"]}, {"cell_type": "markdown", "id": "96de3235", "metadata": {}, "source": ["As you can see, the defined type for each field is float.\n", "\n", "And indeed, if we check the type of the fields, we get the expected result:"]}, {"cell_type": "code", "execution_count": 4, "id": "439cd0c9", "metadata": {}, "outputs": [{"data": {"text/plain": ["float"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["type(p1.x)"]}, {"cell_type": "markdown", "id": "804fb4bc", "metadata": {}, "source": ["But what happens if the data we provide for deserializtion is not an exact type match?\n", "\n", "<div style=\"color: Orange;\">Pydantic will attempt to \"transform\" the data into the correct type - this is called type coercion</div>\n", "\n", "Let's see this:"]}, {"cell_type": "code", "execution_count": 5, "id": "3d9117a7", "metadata": {}, "outputs": [{"data": {"text/plain": ["Coordinates(x=0.0, y=1.2)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["p2 = Coordinates(x=0, y=\"1.2\")\n", "p2"]}, {"cell_type": "markdown", "id": "ebd064b7", "metadata": {}, "source": ["As you can see, Pydantic was able to coerce the integer 0, and the string \"1.2\" to a float value:"]}, {"cell_type": "code", "execution_count": 6, "id": "dee6f757", "metadata": {}, "outputs": [{"data": {"text/plain": ["(float, float)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["type(p2.x), type(p2.y)"]}, {"cell_type": "markdown", "id": "d0378fe2", "metadata": {}, "source": ["Pydantic is not always able to perform the type coercion. In fact, we can even choose the level of type coercion that we find acceptable.\n", "By default, the type coercion is termed lax - and it attempts a variety of type coercions.\n", "But, as we'll see later, we have the option to change that, to a strict mode that is far less forgiving when incorrect data types are provided in the data. Pydantic docs that describes what type coercions will be attempted in either of these modes, is located here: \n", "https://docs.pydantic.dev/latest/concepts/conversion_table/\n", "\n", "If you look at that conversion table, you'll notice, for example, that in lax mode, and dealing with Python data types, input data that is float, int, or Decimal will be coerced to a float. However, strings wil be coerced to floats only under certain conditions.\n", "In strict mode, notice that string to float conversion is not supported (so it will raise a validation error).\n", "Use this table when considering type coercion because things are not always \"obvious\".\n", "For example, with this model:"]}, {"cell_type": "code", "execution_count": 8, "id": "e72991ec", "metadata": {}, "outputs": [], "source": ["class Model(BaseModel):\n", "    field: str"]}, {"cell_type": "markdown", "id": "11e4b628", "metadata": {}, "source": ["We know that all objects in Python have a str() representation, so we might expect to be able to pass any type for field and have Pydantic coerce it to a string.\n", "\n", "But that is not the case:"]}, {"cell_type": "code", "execution_count": 9, "id": "b03ba53e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 validation error for Model\n", "field\n", "  Input should be a valid string [type=string_type, input_value=100, input_type=int]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/string_type\n"]}], "source": ["try:\n", "    Model(field=100)\n", "except ValidationError as ex:\n", "    print(ex)"]}, {"cell_type": "markdown", "id": "217b252a", "metadata": {}, "source": ["And this is probably a good thing as allowing this could lead to unintended problems where we deserialize and object to a string when we never intended for that to happen (our source data maybe changed on us - and auto coercing to a string would hide a potential bug).\n", "\n", "Here's what I mean:\n", "\n", "We are querying a REST API and getting some JSON back from that API, which we model this way:"]}, {"cell_type": "code", "execution_count": 10, "id": "f1874fb3", "metadata": {}, "outputs": [], "source": ["class Contact(BaseModel):\n", "    email: str\n", "initial_json_data = '''\n", "{\n", "    \"email\": \"<EMAIL>\"\n", "}\n", "'''"]}, {"cell_type": "markdown", "id": "e251a15d", "metadata": {}, "source": ["This deserializes just fine:"]}, {"cell_type": "code", "execution_count": 11, "id": "a9a31f7f", "metadata": {}, "outputs": [{"data": {"text/plain": ["Contact(email='<EMAIL>')"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["Contact.model_validate_json(initial_json_data)"]}, {"cell_type": "markdown", "id": "74583c50", "metadata": {}, "source": ["But now, suppose that API changes it's response model, and we are unaware of the change.\n", "\n", "The response data now looks like this:"]}, {"cell_type": "code", "execution_count": 12, "id": "434d0317", "metadata": {}, "outputs": [], "source": ["new_json_data = '''\n", "{\n", "    \"email\": {\n", "        \"personal\": \"<EMAIL>\",\n", "        \"work\": \"<EMAIL>\"\n", "    }\n", "}\n", "'''"]}, {"cell_type": "markdown", "id": "d39c84d6", "metadata": {}, "source": ["Tring to deserialize this data will not work, and we'll immediately know something is wrong with our app (and we can then go in and fix it):"]}, {"cell_type": "code", "execution_count": 13, "id": "f1a292a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 validation error for Contact\n", "email\n", "  Input should be a valid string [type=string_type, input_value={'personal': '<EMAIL>@themint.com'}, input_type=dict]\n", "    For further information visit https://errors.pydantic.dev/2.11/v/string_type\n"]}], "source": ["try:\n", "    Contact.model_validate_json(new_json_data)\n", "except ValidationError as ex:\n", "    print(ex)"]}, {"cell_type": "markdown", "id": "623ddae9", "metadata": {}, "source": ["What if Pydantic had instead just decided to deserialize that email complex object (a nested dictionary basically) into it's string representation?\n", "We can mimic the behavior this way:"]}, {"cell_type": "code", "execution_count": 14, "id": "d0dd433f", "metadata": {}, "outputs": [], "source": ["new_data = {\n", "    \"email\": {\n", "        \"personal\": \"<EMAIL>\",\n", "        \"work\": \"<EMAIL>\"\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 15, "id": "73a2eebf", "metadata": {}, "outputs": [{"data": {"text/plain": ["Contact(email=\"{'personal': '<EMAIL>', 'work': '<EMAIL>'}\")"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["Contact(email=str(new_data['email']))"]}, {"cell_type": "markdown", "id": "a7c9e18c", "metadata": {}, "source": ["\n", "And you can see that we now have a string representation of the email dictionary - no exceptions, and our code is likely to break from that point forward, depending on how we use the email field."]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}