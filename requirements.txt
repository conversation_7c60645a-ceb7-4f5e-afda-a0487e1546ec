# Pydantic V2 Essentials - Dependencies
# Core dependencies for Pydantic V2 learning project

# Main framework
pydantic>=2.0.0

# Jupyter environment for running notebooks
jupyter>=1.0.0
notebook>=6.4.0
ipykernel>=6.0.0

# Additional useful packages for Pydantic development
typing-extensions>=4.0.0

# Development and testing tools
pytest>=7.0.0
pytest-cov>=4.0.0

# Code formatting and linting
black>=22.0.0
isort>=5.10.0
flake8>=5.0.0

# Documentation
mkdocs>=1.4.0
mkdocs-material>=8.0.0

# Optional: For advanced Pydantic features
email-validator>=1.3.0
python-multipart>=0.0.5

# Optional: For working with dates and times
python-dateutil>=2.8.0

# Optional: For JSON schema validation
jsonschema>=4.0.0
